// MPW 密码管理器类型定义

export interface SiteEntry {
  id: string;
  name: string;
  siteName: string;
  counter: number;
  template: PasswordTemplate;
  context?: string;
  lastUsed: number;
  created: number;
}

export type PasswordTemplate = 
  | 'maximum'
  | 'long' 
  | 'medium'
  | 'basic'
  | 'short'
  | 'pin'
  | 'name'
  | 'phrase';

export interface UserProfile {
  name: string;
  sites: SiteEntry[];
  settings: UserSettings;
}

export interface UserSettings {
  defaultTemplate: PasswordTemplate;
  autoLock: boolean;
  autoLockTimeout: number; // 分钟
  showPassword: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface MPWInstance {
  name: string;
  version: number;
  key: Promise<CryptoKey>;
  generate(site: string, counter?: number, context?: string | null, template?: string, NS?: string): Promise<string>;
  generateAuthentication(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  generateIdentification(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  generateRecovery(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  invalidate(): void;
}

// 全局 MPW 类声明
declare global {
  interface Window {
    MPW: {
      new (name: string, password: string, version?: number): MPWInstance;
      VERSION: number;
      NS: string;
      AuthenticationNS: string;
      IdentificationNS: string;
      RecoveryNS: string;
      templates: Record<string, string[]>;
      passchars: Record<string, string>;
      calculateKey(name: string, password: string, version?: number): Promise<CryptoKey>;
      test(): Promise<void>;
    };
    pbkdf2: (password: Uint8Array, salt: Uint8Array, iter: number, keyLen: number, hash: string) => Promise<Uint8Array>;
    scrypt: (password: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, keyLen: number) => Promise<Uint8Array>;
    setImmediate: (callback: () => void) => void;
  }
}

export interface AppState {
  isLoggedIn: boolean;
  currentUser: UserProfile | null;
  mpwInstance: MPWInstance | null;
  selectedSite: SiteEntry | null;
  isLoading: boolean;
  error: string | null;
  showAddSite: boolean;
  showSettings: boolean;
}

export interface GeneratedPassword {
  password: string;
  site: string;
  template: PasswordTemplate;
  counter: number;
  generatedAt: number;
}
