// MPW 密码管理器类型定义

export interface SiteEntry {
  id: string;
  name: string;
  siteName: string;
  counter: number;
  template: PasswordTemplate;
  context?: string;
  lastUsed: number;
  created: number;
}

export type PasswordTemplate =
  | 'maximum'
  | 'long'
  | 'medium'
  | 'basic'
  | 'short'
  | 'pin'
  | 'name'
  | 'phrase';

export interface UserProfile {
  name: string;
  sites: SiteEntry[];
  settings: UserSettings;
}

export interface UserSettings {
  defaultTemplate: PasswordTemplate;
  autoLock: boolean;
  autoLockTimeout: number; // 分钟
  showPassword: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// MPW 实例接口（从 mpw.ts 导入）
export interface MPWInstance {
  name: string;
  version: number;
  key: Promise<CryptoKey | Uint8Array>;
  generate(site: string, counter?: number, context?: string | null, template?: string, NS?: string): Promise<string>;
  generateAuthentication(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  generateIdentification(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  generateRecovery(site: string, counter?: number, context?: string, template?: string): Promise<string>;
  invalidate(): void;
}

export interface AppState {
  isLoggedIn: boolean;
  currentUser: UserProfile | null;
  mpwInstance: MPWInstance | null;
  selectedSite: SiteEntry | null;
  isLoading: boolean;
  error: string | null;
  showAddSite: boolean;
  showSettings: boolean;
}

export interface GeneratedPassword {
  password: string;
  site: string;
  template: PasswordTemplate;
  counter: number;
  generatedAt: number;
}
