# 部署指南

本文档介绍如何部署 MPW 密码管理器到各种平台。

## 本地部署

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:5173 (或显示的端口)
```

### 生产构建
```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

构建完成后，`dist` 目录包含所有静态文件，可以部署到任何静态文件服务器。

## 静态文件服务器部署

### 使用 Python 简单服务器
```bash
# 构建项目
npm run build

# 进入构建目录
cd dist

# 启动 Python 服务器 (Python 3)
python -m http.server 8000

# 或者 Python 2
python -m SimpleHTTPServer 8000

# 访问 http://localhost:8000
```

### 使用 Node.js serve
```bash
# 全局安装 serve
npm install -g serve

# 构建项目
npm run build

# 启动服务器
serve -s dist

# 访问显示的地址
```

## 云平台部署

### Vercel 部署
1. 将代码推送到 GitHub 仓库
2. 访问 [vercel.com](https://vercel.com)
3. 导入 GitHub 仓库
4. Vercel 会自动检测 Vite 项目并部署

或使用 Vercel CLI:
```bash
# 安装 Vercel CLI
npm install -g vercel

# 构建项目
npm run build

# 部署
vercel --prod
```

### Netlify 部署
1. 将代码推送到 GitHub 仓库
2. 访问 [netlify.com](https://netlify.com)
3. 连接 GitHub 仓库
4. 设置构建命令: `npm run build`
5. 设置发布目录: `dist`

或使用拖拽部署:
```bash
# 构建项目
npm run build

# 将 dist 目录拖拽到 Netlify 部署页面
```

### GitHub Pages 部署
1. 在 `vite.config.ts` 中设置 base 路径:
```typescript
export default defineConfig({
  base: '/your-repo-name/',
  // ... 其他配置
})
```

2. 构建并部署:
```bash
# 构建项目
npm run build

# 将 dist 目录内容推送到 gh-pages 分支
# 或使用 GitHub Actions 自动部署
```

### Firebase Hosting 部署
```bash
# 安装 Firebase CLI
npm install -g firebase-tools

# 登录 Firebase
firebase login

# 初始化项目
firebase init hosting

# 构建项目
npm run build

# 部署
firebase deploy
```

## Docker 部署

### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # 支持 SPA 路由
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    }
}
```

### 构建和运行
```bash
# 构建镜像
docker build -t mpw-password-manager .

# 运行容器
docker run -p 8080:80 mpw-password-manager

# 访问 http://localhost:8080
```

## 自托管部署

### Apache 配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/mpw-password-manager
    
    # 支持 SPA 路由
    <Directory /var/www/mpw-password-manager>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # .htaccess 重写规则
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 安全头
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
```

### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/mpw-password-manager;
    index index.html;

    # 支持 SPA 路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

## HTTPS 配置

### 使用 Let's Encrypt (Certbot)
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书 (Nginx)
sudo certbot --nginx -d your-domain.com

# 获取证书 (Apache)
sudo certbot --apache -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 安全建议

1. **HTTPS**: 始终使用 HTTPS 部署，保护数据传输
2. **安全头**: 配置适当的 HTTP 安全头
3. **内容安全策略**: 考虑添加 CSP 头
4. **访问控制**: 如果需要，添加访问控制
5. **定期更新**: 保持依赖项和服务器软件更新

## 环境变量

如果需要配置环境变量，可以在构建时设置:

```bash
# 设置环境变量
export VITE_APP_TITLE="我的密码管理器"
export VITE_APP_VERSION="1.0.0"

# 构建
npm run build
```

在代码中使用:
```typescript
const appTitle = import.meta.env.VITE_APP_TITLE || 'MPW 密码管理器';
```

## 故障排除

### 常见问题

1. **路由问题**: 确保服务器配置支持 SPA 路由
2. **MIME 类型**: 确保服务器正确设置 JavaScript 和 CSS 的 MIME 类型
3. **缓存问题**: 清除浏览器缓存或使用硬刷新
4. **权限问题**: 确保 Web 服务器有读取文件的权限

### 调试

1. 检查浏览器开发者工具的控制台
2. 检查网络标签页的请求状态
3. 验证服务器日志
4. 使用 `npm run preview` 本地测试生产构建

## 监控和维护

1. **日志监控**: 监控服务器访问日志和错误日志
2. **性能监控**: 使用工具如 Google PageSpeed Insights
3. **安全扫描**: 定期进行安全扫描
4. **备份**: 虽然应用是静态的，但考虑备份配置文件
