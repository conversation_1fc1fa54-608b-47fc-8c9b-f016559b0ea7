<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPW TypeScript 版本测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .test-result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        button:hover {
            background: #1d4ed8;
        }
        input, select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        .password-output {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 1.2rem;
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 4px;
            word-break: break-all;
            margin: 1rem 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }
        .comparison-item {
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #d1d5db;
        }
        .js-version {
            background: #fef3c7;
            border-color: #f59e0b;
        }
        .ts-version {
            background: #dbeafe;
            border-color: #3b82f6;
        }
    </style>
</head>
<body>
    <h1>MPW TypeScript 版本测试</h1>
    
    <div class="test-card">
        <h2>TypeScript 版本自测试</h2>
        <p>测试 TypeScript 版本的 MPW 算法是否正确实现</p>
        <button onclick="runTSTest()">运行 TS 版本测试</button>
        <div id="ts-test-result"></div>
    </div>

    <div class="test-card">
        <h2>版本对比测试</h2>
        <p>对比 JavaScript 版本和 TypeScript 版本的输出</p>
        <div>
            <label>用户名:</label>
            <input type="text" id="compare-username" value="testuser" placeholder="输入用户名">
        </div>
        <div>
            <label>主密码:</label>
            <input type="password" id="compare-password" value="testpassword" placeholder="输入主密码">
        </div>
        <div>
            <label>网站:</label>
            <input type="text" id="compare-site" value="example.com" placeholder="输入网站域名">
        </div>
        <div>
            <label>计数器:</label>
            <input type="number" id="compare-counter" value="1" min="1">
        </div>
        <div>
            <label>模板:</label>
            <select id="compare-template">
                <option value="maximum">最大强度</option>
                <option value="long" selected>长密码</option>
                <option value="medium">中等长度</option>
                <option value="basic">基础密码</option>
                <option value="short">短密码</option>
                <option value="pin">数字PIN</option>
                <option value="name">用户名</option>
                <option value="phrase">密码短语</option>
            </select>
        </div>
        
        <button onclick="runComparison()">运行对比测试</button>
        
        <div id="comparison-result"></div>
    </div>

    <div class="test-card">
        <h2>TypeScript 版本性能测试</h2>
        <p>测试 TypeScript 版本的性能</p>
        <button onclick="runTSPerformanceTest()">运行性能测试</button>
        <div id="ts-performance-result"></div>
    </div>

    <div class="test-card">
        <h2>错误处理测试</h2>
        <p>测试 TypeScript 版本的错误处理</p>
        <button onclick="runErrorTests()">运行错误测试</button>
        <div id="error-test-result"></div>
    </div>

    <!-- 加载 JavaScript 版本用于对比 -->
    <script src="/mpw/pbkdf2.js"></script>
    <script src="/mpw/scrypt.js"></script>
    <script src="/mpw/mpw.js"></script>
    
    <!-- 加载 TypeScript 版本 -->
    <script type="module">
        import { MPW, initializeMPW } from '/src/mpw.js';
        
        // 初始化 TypeScript 版本
        initializeMPW();
        
        // 将 TypeScript 版本暴露到全局
        window.MPW_TS = MPW;
        
        // 测试函数
        window.runTSTest = async function() {
            const resultDiv = document.getElementById('ts-test-result');
            resultDiv.innerHTML = '<div class="test-result">运行中...</div>';
            
            try {
                await MPW.test();
                resultDiv.innerHTML = '<div class="test-result success">✅ TypeScript 版本自测试通过！</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ TypeScript 版本自测试失败: ${error.message}</div>`;
            }
        };

        window.runComparison = async function() {
            const resultDiv = document.getElementById('comparison-result');
            resultDiv.innerHTML = '<div class="test-result">对比测试运行中...</div>';
            
            try {
                const username = document.getElementById('compare-username').value;
                const password = document.getElementById('compare-password').value;
                const site = document.getElementById('compare-site').value;
                const counter = parseInt(document.getElementById('compare-counter').value);
                const template = document.getElementById('compare-template').value;
                
                if (!username || !password || !site) {
                    throw new Error('请填写所有必需字段');
                }
                
                // JavaScript 版本
                const startJS = performance.now();
                const mpwJS = new window.MPW(username, password);
                const passwordJS = await mpwJS.generateAuthentication(site, counter, '', template);
                const timeJS = performance.now() - startJS;
                
                // TypeScript 版本
                const startTS = performance.now();
                const mpwTS = new MPW(username, password);
                const passwordTS = await mpwTS.generateAuthentication(site, counter, '', template);
                const timeTS = performance.now() - startTS;
                
                const isMatch = passwordJS === passwordTS;
                
                resultDiv.innerHTML = `
                    <div class="test-result ${isMatch ? 'success' : 'error'}">
                        <h3>${isMatch ? '✅ 对比测试通过' : '❌ 对比测试失败'}</h3>
                        <div class="comparison">
                            <div class="comparison-item js-version">
                                <h4>JavaScript 版本</h4>
                                <div class="password-output">${passwordJS}</div>
                                <p>生成时间: ${timeJS.toFixed(2)} ms</p>
                            </div>
                            <div class="comparison-item ts-version">
                                <h4>TypeScript 版本</h4>
                                <div class="password-output">${passwordTS}</div>
                                <p>生成时间: ${timeTS.toFixed(2)} ms</p>
                            </div>
                        </div>
                        <p><strong>结果匹配:</strong> ${isMatch ? '是' : '否'}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ 对比测试失败: ${error.message}</div>`;
            }
        };

        window.runTSPerformanceTest = async function() {
            const resultDiv = document.getElementById('ts-performance-result');
            resultDiv.innerHTML = '<div class="test-result">性能测试运行中...</div>';
            
            try {
                // 测试密钥生成性能
                const startKey = performance.now();
                const mpw = new MPW('testuser', 'testpassword');
                await mpw.key;
                const keyTime = performance.now() - startKey;
                
                // 测试密码生成性能
                const startPassword = performance.now();
                await mpw.generateAuthentication('example.com', 1, '', 'long');
                const passwordTime = performance.now() - startPassword;
                
                // 测试多次密码生成
                const iterations = 10;
                const startMultiple = performance.now();
                for (let i = 0; i < iterations; i++) {
                    await mpw.generateAuthentication(`site${i}.com`, 1, '', 'long');
                }
                const multipleTime = performance.now() - startMultiple;
                
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <h3>TypeScript 版本性能测试结果</h3>
                        <ul>
                            <li>密钥生成时间: ${keyTime.toFixed(2)} ms</li>
                            <li>单次密码生成时间: ${passwordTime.toFixed(2)} ms</li>
                            <li>${iterations}次密码生成总时间: ${multipleTime.toFixed(2)} ms</li>
                            <li>平均每次密码生成: ${(multipleTime / iterations).toFixed(2)} ms</li>
                        </ul>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ 性能测试失败: ${error.message}</div>`;
            }
        };

        window.runErrorTests = async function() {
            const resultDiv = document.getElementById('error-test-result');
            resultDiv.innerHTML = '<div class="test-result">错误测试运行中...</div>';
            
            const tests = [];
            
            // 测试空用户名
            try {
                new MPW('', 'password');
                tests.push('❌ 空用户名测试失败 - 应该抛出错误');
            } catch (error) {
                tests.push('✅ 空用户名测试通过 - ' + error.message);
            }
            
            // 测试空密码
            try {
                new MPW('user', '');
                tests.push('❌ 空密码测试失败 - 应该抛出错误');
            } catch (error) {
                tests.push('✅ 空密码测试通过 - ' + error.message);
            }
            
            // 测试无效版本
            try {
                new MPW('user', 'password', 999);
                tests.push('❌ 无效版本测试失败 - 应该抛出错误');
            } catch (error) {
                tests.push('✅ 无效版本测试通过 - ' + error.message);
            }
            
            // 测试无效模板
            try {
                const mpw = new MPW('user', 'password');
                await mpw.generateAuthentication('site.com', 1, '', 'invalid');
                tests.push('❌ 无效模板测试失败 - 应该抛出错误');
            } catch (error) {
                tests.push('✅ 无效模板测试通过 - ' + error.message);
            }
            
            resultDiv.innerHTML = `
                <div class="test-result success">
                    <h3>错误处理测试结果</h3>
                    <ul>
                        ${tests.map(test => `<li>${test}</li>`).join('')}
                    </ul>
                </div>
            `;
        };

        // 页面加载时自动运行 TypeScript 版本测试
        window.addEventListener('load', () => {
            setTimeout(runTSTest, 500);
        });
    </script>
</body>
</html>
