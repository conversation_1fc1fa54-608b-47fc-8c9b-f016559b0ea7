# MPW 密码管理器

基于 Master Password (MPW) 算法的现代化密码管理器界面，使用 TypeScript 和 Vite 构建。

## 功能特性

### 🔐 安全特性
- **主密码算法**: 基于 MPW v3 算法，使用 scrypt 密钥派生
- **无密码存储**: 主密码不会被存储，仅用于生成网站密码
- **确定性生成**: 相同的输入总是产生相同的密码
- **自动锁定**: 可配置的自动锁定功能保护您的会话

### 🌐 密码管理
- **多种密码模板**: 支持8种不同强度和格式的密码模板
  - 最大强度 (20字符，包含所有字符类型)
  - 长密码 (14字符，易记忆格式)
  - 中等长度 (8字符，平衡安全性和便利性)
  - 基础密码 (8字符，仅字母和数字)
  - 短密码 (4字符，用于PIN码)
  - 数字PIN (4位数字)
  - 用户名 (9字符，适合用户名)
  - 密码短语 (多个单词组成)

- **网站管理**: 添加、编辑、删除网站条目
- **计数器支持**: 为每个网站设置不同的计数器值
- **上下文支持**: 为同一网站的不同账户设置上下文

### 💻 用户界面
- **现代化设计**: 响应式设计，支持桌面和移动设备
- **暗色主题**: 自动适应系统主题偏好
- **键盘快捷键**:
  - `Ctrl/Cmd + G`: 生成密码
  - `Ctrl/Cmd + C`: 复制密码
  - `Ctrl/Cmd + N`: 添加新网站
  - `Escape`: 关闭模态框
- **一键复制**: 快速复制生成的密码到剪贴板

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 使用指南

### 1. 登录
- 输入您的用户名（用于区分不同用户的配置）
- 输入您的主密码（这是唯一需要记住的密码）
- 点击"登录"

### 2. 添加网站
- 点击"添加网站"按钮
- 填写网站名称（显示名称）
- 填写网站域名（用于密码生成的唯一标识符）
- 选择密码模板
- 可选：设置上下文（用于区分同一网站的不同账户）

### 3. 生成密码
- 从左侧列表选择一个网站
- 点击"生成密码"按钮
- 密码将显示在输入框中
- 点击"复制密码"将密码复制到剪贴板

### 4. 管理网站
- 修改计数器：如果需要更改密码，增加计数器值
- 更改模板：选择不同的密码强度和格式
- 设置上下文：为同一网站的不同账户生成不同密码
- 删除网站：移除不再需要的网站条目

### 5. 设置
- 默认密码模板：新添加网站时的默认模板
- 自动锁定：启用后在指定时间内无操作将自动退出
- 自动锁定时间：设置自动锁定的时间间隔
- 默认显示密码：控制密码是否默认以明文显示

## 技术架构

### 前端技术栈
- **TypeScript**: 类型安全的 JavaScript，完整的 MPW 算法 TypeScript 重写
- **Vite**: 快速的构建工具和开发服务器
- **CSS Variables**: 现代化的样式系统，支持主题切换
- **Web Crypto API**: 浏览器原生加密API

### MPW 算法实现 (TypeScript 版本)
- **完整重写**: 将原 JavaScript 版本完全重写为 TypeScript
- **类型安全**: 提供完整的类型定义和接口
- **错误处理**: 改进的错误处理和验证机制
- **模块化设计**: 清晰的模块分离和依赖管理
- **scrypt**: 密钥派生函数，防止暴力破解
- **HMAC-SHA256**: 消息认证码，确保密码生成的安全性
- **确定性算法**: 保证相同输入产生相同输出

### 数据存储
- **LocalStorage**: 存储用户配置和网站列表
- **无敏感数据**: 主密码和生成的密码不会被存储
- **用户隔离**: 不同用户的数据完全隔离

## 安全说明

1. **主密码安全**: 选择一个强壮且唯一的主密码，这是整个系统安全的基础
2. **本地存储**: 所有数据都存储在本地，不会上传到任何服务器
3. **密码不存储**: 生成的密码不会被保存，每次都是实时计算
4. **算法标准**: 使用经过验证的 MPW 算法，与官方实现兼容

## 兼容性

- **浏览器要求**: 支持 ES2017+ 和 Web Crypto API 的现代浏览器
- **MPW 兼容**: 与 Master Password v3 算法完全兼容
- **跨平台**: 可在 Windows、macOS、Linux 上运行

## 许可证

本项目基于 Creative Commons Attribution 4.0 International License 许可证。

MPW 算法实现基于 Tom Thorogood 的工作，遵循相同的许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 免责声明

请确保在安全的环境中使用此密码管理器，并定期备份您的网站配置。虽然主密码不会被存储，但建议您记住或安全地备份您的主密码。
