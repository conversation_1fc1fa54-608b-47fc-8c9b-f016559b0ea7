<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 MPW TypeScript 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            white-space: pre-wrap;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
            color: #166534;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <h1>简单 MPW TypeScript 测试</h1>
    
    <div class="test-card">
        <h2>基础功能测试</h2>
        <button onclick="testBasic()">测试基础功能</button>
        <div id="basic-result"></div>
    </div>

    <div class="test-card">
        <h2>错误处理测试</h2>
        <button onclick="testErrors()">测试错误处理</button>
        <div id="error-result"></div>
    </div>

    <script type="module">
        // 直接导入 TypeScript 模块
        import { MPW, initializeMPW } from './src/mpw.js';
        
        // 初始化
        initializeMPW();
        
        // 暴露测试函数到全局
        window.testBasic = async function() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<div class="result">测试中...</div>';
            
            try {
                console.log('开始基础测试...');
                
                // 测试 MPW 类是否可以实例化
                console.log('创建 MPW 实例...');
                const mpw = new MPW('testuser', 'testpassword');
                console.log('MPW 实例创建成功:', mpw);
                
                // 测试密钥生成
                console.log('等待密钥生成...');
                const key = await mpw.key;
                console.log('密钥生成成功:', key);
                
                // 测试密码生成
                console.log('生成密码...');
                const password = await mpw.generateAuthentication('example.com', 1, '', 'long');
                console.log('密码生成成功:', password);
                
                // 测试自测试
                console.log('运行自测试...');
                await MPW.test();
                console.log('自测试通过');
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 基础功能测试通过！
                        
                        生成的密码: ${password}
                        密钥类型: ${key.constructor.name}
                        MPW 版本: ${MPW.VERSION}
                    </div>
                `;
            } catch (error) {
                console.error('基础测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 基础功能测试失败:
                        ${error.message}
                        
                        堆栈跟踪:
                        ${error.stack}
                    </div>
                `;
            }
        };

        window.testErrors = async function() {
            const resultDiv = document.getElementById('error-result');
            resultDiv.innerHTML = '<div class="result">测试中...</div>';
            
            const tests = [];
            
            try {
                // 测试空用户名
                try {
                    new MPW('', 'password');
                    tests.push('❌ 空用户名测试失败 - 应该抛出错误');
                } catch (error) {
                    tests.push(`✅ 空用户名测试通过 - ${error.message}`);
                }
                
                // 测试空密码
                try {
                    new MPW('user', '');
                    tests.push('❌ 空密码测试失败 - 应该抛出错误');
                } catch (error) {
                    tests.push(`✅ 空密码测试通过 - ${error.message}`);
                }
                
                // 测试无效版本
                try {
                    new MPW('user', 'password', 999);
                    tests.push('❌ 无效版本测试失败 - 应该抛出错误');
                } catch (error) {
                    tests.push(`✅ 无效版本测试通过 - ${error.message}`);
                }
                
                // 测试模板验证
                const isValidLong = MPW.isValidTemplate('long');
                const isValidInvalid = MPW.isValidTemplate('invalid');
                tests.push(`✅ 模板验证测试 - long: ${isValidLong}, invalid: ${isValidInvalid}`);
                
                // 测试版本验证
                const isValid3 = MPW.isValidVersion(3);
                const isValid999 = MPW.isValidVersion(999);
                tests.push(`✅ 版本验证测试 - 3: ${isValid3}, 999: ${isValid999}`);
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        错误处理测试结果:
                        
                        ${tests.join('\n')}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 错误处理测试失败:
                        ${error.message}
                    </div>
                `;
            }
        };

        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('页面加载完成，开始自动测试...');
                testBasic();
            }, 500);
        });
    </script>
</body>
</html>
