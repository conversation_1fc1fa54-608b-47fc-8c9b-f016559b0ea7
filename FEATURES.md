# MPW 密码管理器功能特性

## 🎯 项目概述

基于 Master Password (MPW) 算法的现代化密码管理器，提供安全、便捷的密码生成和管理功能。使用 TypeScript + Vite 构建，具有现代化的用户界面和完整的功能集。

## 🔐 核心安全特性

### MPW 算法实现
- **算法版本**: 完全兼容 MPW v3 算法
- **密钥派生**: 使用 scrypt 算法，参数 N=32768, r=8, p=2
- **密码生成**: 基于 HMAC-SHA256 的确定性密码生成
- **无状态设计**: 不存储任何密码，每次实时计算

### 安全机制
- **主密码保护**: 主密码不会被存储在任何地方
- **本地计算**: 所有密码生成都在本地完成
- **自动锁定**: 可配置的自动锁定功能
- **会话管理**: 安全的会话超时机制

## 🌟 用户界面特性

### 现代化设计
- **响应式布局**: 适配桌面和移动设备
- **暗色主题**: 自动适应系统主题偏好
- **直观操作**: 简洁明了的用户界面
- **无障碍支持**: 符合 Web 无障碍标准

### 交互体验
- **实时反馈**: 操作状态的即时反馈
- **键盘快捷键**: 提高操作效率
- **一键复制**: 快速复制密码到剪贴板
- **拖拽支持**: 支持文件拖拽操作

## 🔑 密码管理功能

### 密码模板系统
1. **最大强度** (maximum): 20字符，包含所有字符类型
   - 示例: `w6Jb@4rC9#kL8mN3$pQ7`
   - 适用: 高安全要求的账户

2. **长密码** (long): 14字符，易记忆的格式
   - 示例: `ZedaFaxcZaso9*`
   - 适用: 大多数网站账户

3. **中等长度** (medium): 8字符，平衡安全性和便利性
   - 示例: `ZedFax4*`
   - 适用: 一般网站账户

4. **基础密码** (basic): 8字符，仅字母和数字
   - 示例: `zedfax94`
   - 适用: 限制特殊字符的系统

5. **短密码** (short): 4字符，用于PIN码
   - 示例: `Zed4`
   - 适用: PIN码或简单密码

6. **数字PIN** (pin): 4位数字
   - 示例: `9427`
   - 适用: 数字PIN码

7. **用户名** (name): 9字符，适合用户名
   - 示例: `zedfaxcas`
   - 适用: 用户名生成

8. **密码短语** (phrase): 多个单词组成
   - 示例: `zedf axc zaso cav`
   - 适用: 安全问题答案

### 网站管理
- **添加网站**: 快速添加新的网站条目
- **编辑信息**: 修改网站名称、域名、模板等
- **计数器管理**: 为每个网站设置独立的计数器
- **上下文支持**: 为同一网站的不同账户设置上下文
- **批量操作**: 支持批量导入导出（计划中）

### 高级功能
- **搜索过滤**: 快速查找特定网站
- **使用统计**: 显示最近使用的网站
- **备份恢复**: 配置数据的备份和恢复
- **同步支持**: 跨设备配置同步（计划中）

## ⚙️ 配置和设置

### 用户设置
- **默认模板**: 设置新网站的默认密码模板
- **自动锁定**: 配置自动锁定时间
- **显示选项**: 控制密码显示方式
- **主题选择**: 明亮/暗色主题切换

### 安全设置
- **会话超时**: 自定义会话超时时间
- **密码显示**: 控制密码是否默认显示
- **操作确认**: 重要操作的确认机制
- **错误处理**: 完善的错误提示和处理

## 🚀 技术特性

### 前端技术栈
- **TypeScript**: 类型安全的开发体验
- **Vite**: 快速的构建工具和热重载
- **现代 CSS**: CSS Variables 和 Grid/Flexbox 布局
- **Web APIs**: 充分利用现代浏览器 API

### 性能优化
- **懒加载**: 按需加载组件和资源
- **缓存策略**: 智能的缓存机制
- **代码分割**: 优化的代码分割策略
- **压缩优化**: 生产环境的资源压缩

### 兼容性
- **浏览器支持**: 支持所有现代浏览器
- **移动设备**: 完整的移动设备支持
- **PWA 就绪**: 可作为 PWA 安装使用
- **离线功能**: 完全的离线工作能力

## 🔧 开发者功能

### 调试工具
- **算法测试**: 内置的 MPW 算法测试页面
- **性能监控**: 密钥生成和密码生成性能测试
- **错误追踪**: 详细的错误日志和调试信息
- **开发模式**: 开发环境的额外调试功能

### 扩展性
- **模块化设计**: 清晰的模块化架构
- **插件系统**: 支持功能插件扩展（计划中）
- **API 接口**: 标准化的内部 API
- **主题系统**: 可扩展的主题系统

## 📱 移动端特性

### 触摸优化
- **触摸友好**: 优化的触摸交互
- **手势支持**: 支持常用手势操作
- **屏幕适配**: 完美适配各种屏幕尺寸
- **性能优化**: 移动设备性能优化

### 移动端功能
- **快速访问**: 移动端快速访问功能
- **分享支持**: 支持系统分享功能
- **剪贴板集成**: 与系统剪贴板深度集成
- **通知支持**: 操作完成通知

## 🔒 隐私保护

### 数据处理
- **本地存储**: 所有数据仅存储在本地
- **无网络传输**: 不向任何服务器发送敏感数据
- **用户控制**: 用户完全控制自己的数据
- **透明处理**: 数据处理过程完全透明

### 隐私特性
- **无追踪**: 不包含任何追踪代码
- **无分析**: 不收集用户使用数据
- **开源透明**: 完全开源，代码可审计
- **隐私优先**: 隐私保护优先的设计理念

## 🎨 自定义功能

### 界面自定义
- **主题定制**: 支持自定义主题色彩
- **布局调整**: 可调整的界面布局
- **字体设置**: 自定义字体大小和样式
- **密度选项**: 界面密度调整选项

### 功能定制
- **快捷键**: 可自定义的键盘快捷键
- **默认设置**: 个性化的默认设置
- **工作流程**: 自定义的操作工作流程
- **导入导出**: 灵活的数据导入导出

## 🌐 国际化支持

### 多语言
- **中文支持**: 完整的中文界面
- **英文支持**: 标准英文界面
- **扩展性**: 易于添加新语言支持
- **本地化**: 完整的本地化支持

### 地区适配
- **时间格式**: 适配本地时间格式
- **数字格式**: 适配本地数字格式
- **文化适配**: 考虑文化差异的界面设计
- **可访问性**: 符合各地区无障碍标准

## 📈 未来规划

### 短期目标
- **性能优化**: 进一步优化性能
- **功能完善**: 完善现有功能
- **用户体验**: 提升用户体验
- **错误修复**: 修复已知问题

### 长期目标
- **云同步**: 可选的云端同步功能
- **团队功能**: 团队密码管理功能
- **高级安全**: 更多高级安全特性
- **生态系统**: 构建完整的密码管理生态系统
